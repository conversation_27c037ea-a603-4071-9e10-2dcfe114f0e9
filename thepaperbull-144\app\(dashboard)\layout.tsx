"use client"

import type React from "react"

import { useEffect, useState, Suspense, useRef } from "react"
import { useRouter } from "next/navigation"
import { Bell } from "lucide-react"
import SideNavigation from "@/components/side-navigation"
import AccountDropdown from "@/components/account-dropdown"
import MobileSidebarToggle from "@/components/mobile-sidebar-toggle"
import { TradingProvider } from "@/contexts/trading-context"
import { useAuth } from "@/contexts/auth-context"

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false)
  const [sidebarVisible, setSidebarVisible] = useState(true)
  const [isClient, setIsClient] = useState(false)

  const notificationDropdownRef = useRef<HTMLDivElement>(null)
  const { user, loading } = useAuth()

  // Check if user is logged in using Firebase Auth
  useEffect(() => {
    setIsClient(true)

    if (!loading) {
      if (!user) {
        router.push("/login")
      } else {
        setIsLoading(false)
      }
    }
  }, [user, loading, router])

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("mobile-sidebar")
      const toggle = document.getElementById("mobile-sidebar-toggle")

      if (
        mobileSidebarOpen &&
        sidebar &&
        toggle &&
        !sidebar.contains(event.target as Node) &&
        !toggle.contains(event.target as Node)
      ) {
        setMobileSidebarOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [mobileSidebarOpen])

  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (mobileSidebarOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }

    return () => {
      document.body.style.overflow = "auto"
    }
  }, [mobileSidebarOpen])

  // Close notification dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target as Node)) {
        setNotificationDropdownOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Handle window resize to update sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      // Force re-render to update sidebar width calculation
      setIsClient(true)
    }

    if (isClient) {
      window.addEventListener("resize", handleResize)
      return () => window.removeEventListener("resize", handleResize)
    }
  }, [isClient])

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <svg
            className="animate-spin h-12 w-12 text-primary mx-auto"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="mt-4 text-lg font-medium text-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <TradingProvider>
      <div className="min-h-screen w-full bg-background text-foreground">
        <div className="h-full w-full">
          <div className="bg-card shadow-lg h-full">
          {/* Header */}
          <header className="flex items-center justify-between p-responsive border-b border-border lg:hidden">
            <div className="flex items-center space-x-4">
              <div id="mobile-sidebar-toggle">
                <MobileSidebarToggle onToggle={setMobileSidebarOpen} isOpen={mobileSidebarOpen} />
              </div>
              <div className="flex items-center">
                <h1 className="text-primary font-bold text-responsive-lg tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Trading Platform
                </h1>
                <span className="ml-1 text-2xs bg-primary/20 text-primary px-1.5 py-0.5 rounded-md">BETA</span>
              </div>
            </div>

            <div className="flex items-center space-x-5">
              <div className="relative" ref={notificationDropdownRef}>
                <button
                  className="relative p-1.5 rounded-full hover:bg-muted focus:outline-none transition-colors"
                  onClick={() => setNotificationDropdownOpen(!notificationDropdownOpen)}
                  aria-label="Notifications"
                >
                  <Bell className="h-6 w-6 text-foreground" />
                  <span className="absolute -top-0.5 -right-0.5 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
                    12
                  </span>
                </button>

                {notificationDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-80 bg-card rounded-lg shadow-lg border border-border z-50 animate-in fade-in slide-in-from-top-5 duration-200">
                    <div className="p-3 border-b border-border">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">Notifications</h3>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-primary hover:text-primary/90 cursor-pointer">
                            Mark all as read
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="max-h-[350px] overflow-y-auto">{/* Notification items */}</div>

                    <div className="p-2 border-t border-border text-center">
                      <button className="text-sm text-primary hover:text-primary/90 font-medium">
                        View all notifications
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <AccountDropdown />
            </div>
          </header>

          <div className="relative h-[calc(100vh-57px)]">
            {/* Mobile Sidebar Overlay */}
            {mobileSidebarOpen && <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-20 md:hidden" />}

            {/* Sidebar - Fixed positioning to make it independent */}
            <div
              id="mobile-sidebar"
              className={`${
                mobileSidebarOpen ? "translate-x-0" : "-translate-x-full"
              } fixed inset-y-0 left-0 z-30 w-80 transform transition-all duration-300 ease-in-out md:translate-x-0 shadow-xl`}
              style={{
                top: "57px", // Account for header height
                height: "calc(100vh - 57px)",
                transform: isClient && window.innerWidth >= 768
                  ? `translateX(${sidebarVisible ? "0" : "-100%"})`
                  : mobileSidebarOpen ? "translateX(0)" : "translateX(-100%)"
              }}
            >
              <SideNavigation onCloseMobile={() => setMobileSidebarOpen(false)} />
            </div>

            {/* Sidebar Toggle Button (Desktop) - Fixed position */}
            <div className="hidden md:flex fixed left-0 top-1/2 transform -translate-y-1/2 z-40">
              <button
                onClick={() => setSidebarVisible(!sidebarVisible)}
                className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-r-xl p-3 shadow-lg hover:shadow-xl transition-all duration-300 group"
                style={{
                  transform: `translateX(${sidebarVisible ? "320px" : "0px"})` // 320px = w-80
                }}
                aria-label={sidebarVisible ? "Hide sidebar" : "Show sidebar"}
              >
                {sidebarVisible ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="group-hover:scale-110 transition-transform duration-200"
                  >
                    <path d="m15 18-6-6 6-6" />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="group-hover:scale-110 transition-transform duration-200"
                  >
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                )}
              </button>
            </div>

            {/* Main Content - Independent of sidebar */}
            <div className="w-full h-full overflow-auto">
              <Suspense fallback={<div>Loading...</div>}>{children}</Suspense>
            </div>
          </div>
          </div>
        </div>
      </div>
    </TradingProvider>
  )
}

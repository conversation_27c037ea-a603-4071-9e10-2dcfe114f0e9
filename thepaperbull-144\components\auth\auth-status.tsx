"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import userService from '@/lib/user-service'

export function AuthStatus() {
  const { user: firebaseUser, loading } = useAuth()
  const [user, setUser] = useState<any>(null)
  const [userLoading, setUserLoading] = useState(true)
  const router = useRouter()

  // Subscribe to user service for user data
  useEffect(() => {
    const unsubscribe = userService.subscribe((userData) => {
      setUser(userData)
      setUserLoading(false)
    })
    return unsubscribe
  }, [])

  // Show loading state while authentication is being checked
  if (loading || userLoading) {
    return (
      <div className="flex items-center gap-1">
        <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
        <span className="text-xs text-muted-foreground">Checking...</span>
      </div>
    )
  }

  // If Firebase user exists, consider authenticated (user service data may take time to load)
  if (!firebaseUser) {
    return (
      <div className="flex items-center gap-1">
        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
        <span className="text-xs text-red-600 dark:text-red-400">Not Auth</span>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-1">
      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
      <span className="text-xs text-green-600 dark:text-green-400">
        {user?.balance?.current || 10000} USDT
      </span>
    </div>
  )
}

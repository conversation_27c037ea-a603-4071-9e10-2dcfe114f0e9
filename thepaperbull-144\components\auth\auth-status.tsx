"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import userService from '@/lib/user-service'

export function AuthStatus() {
  const { user: firebaseUser, loading } = useAuth()
  const [user, setUser] = useState<any>(null)
  const [userLoading, setUserLoading] = useState(true)
  const router = useRouter()

  // Subscribe to user service for user data
  useEffect(() => {
    const unsubscribe = userService.subscribe((userData) => {
      setUser(userData)
      setUserLoading(false)
    })
    return unsubscribe
  }, [])

  // Show loading state while authentication is being checked
  if (loading || userLoading) {
    return (
      <div className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <Badge variant="secondary">Checking Authentication...</Badge>
        <span className="text-sm text-blue-700 dark:text-blue-400">
          Please wait...
        </span>
      </div>
    )
  }

  // If Firebase user exists, consider authenticated (user service data may take time to load)
  if (!firebaseUser) {
    return (
      <div className="flex items-center gap-2 p-2 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
        <Badge variant="destructive">Not Authenticated</Badge>
        <span className="text-sm text-red-700 dark:text-red-400">
          Please sign in to trade
        </span>
        <Button
          size="sm"
          variant="outline"
          onClick={() => router.push('/login')}
        >
          Sign In
        </Button>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
      <Badge variant="default" className="bg-green-600">Authenticated</Badge>
      <span className="text-sm text-green-700 dark:text-green-400">
        {user?.email || firebaseUser.email} • {user?.balance?.current || 10000} USDT
      </span>
    </div>
  )
}

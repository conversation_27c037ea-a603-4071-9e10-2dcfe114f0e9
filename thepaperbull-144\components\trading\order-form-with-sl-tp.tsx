"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { ChevronDown, ChevronUp, Shield } from "lucide-react"
import { useTrading } from "../../contexts/trading-context"
import type { OrderRequest } from "../../types/trading"
import { toast } from "sonner"

interface OrderFormProps {
  symbol: string
  lastPrice: string
  leverage?: number
  onOrderSubmit?: (order: any) => void
}

export default function OrderForm({ symbol, lastPrice, leverage: propLeverage, onOrderSubmit }: OrderFormProps) {
  const { placeOrder, isLoading, accountInfo, getAvailableBalance } = useTrading()

  const [orderType, setOrderType] = useState<"MARKET" | "LIMIT">("MARKET")
  const [side, setSide] = useState<"BUY" | "SELL">("BUY")
  const [quantity, setQuantity] = useState("")
  const [price, setPrice] = useState(lastPrice)
  const [percentButtons, setPercentButtons] = useState([25, 50, 75, 100])
  const [stopLoss, setStopLoss] = useState("")
  const [takeProfit, setTakeProfit] = useState("")
  const [slPercent, setSlPercent] = useState(0)
  const [tpPercent, setTpPercent] = useState(0)
  const [showSlTp, setShowSlTp] = useState(false)

  // Use leverage from props or default to 10
  const leverage = propLeverage || 10

  // Get real-time balance from trading context with fallbacks
  const totalBalance = accountInfo?.totalWalletBalance || 10000
  let availableBalance = getAvailableBalance()

  // Fallback calculation if available balance is 0 or null
  if (availableBalance === null || availableBalance === undefined || availableBalance === 0) {
    const totalPnL = accountInfo?.totalUnrealizedProfit || 0
    const usedMargin = accountInfo?.totalPositionInitialMargin || 0
    availableBalance = Math.max(0, totalBalance - usedMargin + totalPnL)
  }

  // Update price when lastPrice changes
  useEffect(() => {
    if (orderType === "MARKET") {
      setPrice(lastPrice)
    }
  }, [lastPrice, orderType])

  // Calculate max quantity based on available balance and leverage
  const calculateMaxQuantity = () => {
    if (!lastPrice || availableBalance <= 0) return "0"
    const maxQty = ((availableBalance * leverage) / Number.parseFloat(lastPrice)).toFixed(4)
    return maxQty
  }

  // Handle percent buttons
  const handlePercentClick = (percent: number) => {
    const maxQty = calculateMaxQuantity()
    const calculatedQty = ((Number.parseFloat(maxQty) * percent) / 100).toFixed(4)
    setQuantity(calculatedQty)
  }

  // Auto-calculate SL/TP based on a percentage from entry price
  const calculateSlTp = (percent: number, type: "sl" | "tp") => {
    if (!price) return

    const priceNum = Number.parseFloat(price)
    if (type === "sl") {
      setSlPercent(percent)
      if (side === "BUY") {
        // For long positions, SL is below entry price
        setStopLoss((priceNum * (1 - percent / 100)).toFixed(2))
      } else {
        // For short positions, SL is above entry price
        setStopLoss((priceNum * (1 + percent / 100)).toFixed(2))
      }
    } else {
      setTpPercent(percent)
      if (side === "BUY") {
        // For long positions, TP is above entry price
        setTakeProfit((priceNum * (1 + percent / 100)).toFixed(2))
      } else {
        // For short positions, TP is below entry price
        setTakeProfit((priceNum * (1 - percent / 100)).toFixed(2))
      }
    }
  }

  // Calculate potential profit/loss
  const calculatePotentialPnl = () => {
    if (!quantity || !price || (!stopLoss && !takeProfit)) return { sl: "0", tp: "0" }

    const qty = Number.parseFloat(quantity)
    const entryPrice = Number.parseFloat(price)
    const sl = stopLoss ? Number.parseFloat(stopLoss) : 0
    const tp = takeProfit ? Number.parseFloat(takeProfit) : 0

    let slPnl = 0
    let tpPnl = 0

    if (side === "BUY") {
      // Long position
      if (sl) slPnl = (sl - entryPrice) * qty
      if (tp) tpPnl = (tp - entryPrice) * qty
    } else {
      // Short position
      if (sl) slPnl = (entryPrice - sl) * qty
      if (tp) tpPnl = (entryPrice - tp) * qty
    }

    return {
      sl: slPnl.toFixed(2),
      tp: tpPnl.toFixed(2),
    }
  }

  // Handle order submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!quantity || Number.parseFloat(quantity) <= 0) {
      toast.error("Please enter a valid quantity")
      return
    }

    if (orderType === "LIMIT" && (!price || Number.parseFloat(price) <= 0)) {
      toast.error("Please enter a valid price")
      return
    }

    // Check if user has sufficient balance
    const orderPrice = orderType === "MARKET" ? Number.parseFloat(lastPrice) : Number.parseFloat(price)
    const orderValue = Number.parseFloat(quantity) * orderPrice
    const requiredMargin = orderValue / leverage

    if (requiredMargin > availableBalance) {
      toast.error(`Insufficient balance. Required: ${requiredMargin.toFixed(2)} USDT, Available: ${availableBalance.toFixed(2)} USDT`)
      return
    }

    const orderRequest: OrderRequest = {
      symbol,
      side,
      type: orderType,
      quantity: Number.parseFloat(quantity),
      leverage,
    }

    if (orderType === "LIMIT") {
      orderRequest.price = Number.parseFloat(price)
    }

    if (stopLoss) {
      orderRequest.stopLoss = Number.parseFloat(stopLoss)
    }

    if (takeProfit) {
      orderRequest.takeProfit = Number.parseFloat(takeProfit)
    }

    try {
      console.log('Submitting order from form:', orderRequest)
      const orderId = await placeOrder(orderRequest)
      console.log('Order submitted successfully with ID:', orderId)

      // Call the optional callback if provided
      if (onOrderSubmit) {
        onOrderSubmit(orderRequest)
      }

      // Reset form after successful submission
      setQuantity("")
      if (orderType === "LIMIT") {
        setPrice(lastPrice)
      }
      setStopLoss("")
      setTakeProfit("")
      setSlPercent(0)
      setTpPercent(0)

      toast.success(`${side} order placed successfully`, {
        description: `${quantity} ${symbol} at ${orderType === "MARKET" ? "market price" : `$${price}`}`,
        duration: 5000
      })
    } catch (error) {
      console.error("Error submitting order:", error)

      // Show more specific error message
      const errorMessage = error instanceof Error ? error.message : "Failed to submit order. Please try again."

      toast.error(errorMessage, {
        duration: 8000,
        description: "Please check your connection and try again."
      })
    }
  }

  const potentialPnl = calculatePotentialPnl()

  // Calculate if user has sufficient balance for current order
  const orderPrice = orderType === "MARKET" ? Number.parseFloat(lastPrice) : Number.parseFloat(price)
  const orderValue = Number.parseFloat(quantity || "0") * orderPrice
  const requiredMargin = orderValue / leverage
  const hasSufficientBalance = requiredMargin <= availableBalance

  return (
    <div className="bg-card rounded-xl border border-border/50 shadow-lg p-4 h-full backdrop-blur-sm">
      <div className="mb-4">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-bold text-foreground tracking-tight">Place Order</h2>
          <div className="text-right">
            <div className="font-bold text-emerald-600 text-sm">{availableBalance.toFixed(2)} USDT</div>
            <div className="text-xs text-muted-foreground">Available</div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="flex flex-col h-[calc(100%-80px)]">
        {/* Order Type Selector - Compact */}
        <div className="mb-4">
          <label className="block text-xs font-semibold text-foreground mb-1.5">Order Type</label>
          <div className="flex rounded-md overflow-hidden border border-border/50 bg-muted/20">
            <button
              type="button"
              className={`flex-1 py-2.5 text-center text-xs font-semibold transition-all duration-200 ${
                orderType === "MARKET"
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "bg-transparent text-muted-foreground hover:bg-muted/50 hover:text-foreground"
              }`}
              onClick={() => setOrderType("MARKET")}
            >
              Market
            </button>
            <button
              type="button"
              className={`flex-1 py-2.5 text-center text-xs font-semibold transition-all duration-200 ${
                orderType === "LIMIT"
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "bg-transparent text-muted-foreground hover:bg-muted/50 hover:text-foreground"
              }`}
              onClick={() => setOrderType("LIMIT")}
            >
              Limit
            </button>
          </div>
        </div>

        {/* Buy/Sell Buttons - Compact */}
        <div className="mb-4">
          <label className="block text-xs font-semibold text-foreground mb-2">Order Side</label>
          <div className="grid grid-cols-2 gap-2">
            <button
              type="button"
              className={`py-2 px-3 rounded-lg font-semibold text-xs transition-all duration-200 ${
                side === "BUY"
                  ? "bg-emerald-600 text-white shadow-md shadow-emerald-600/25"
                  : "bg-emerald-50 dark:bg-emerald-950/20 border border-emerald-200 dark:border-emerald-800 text-emerald-700 dark:text-emerald-400 hover:bg-emerald-100 dark:hover:bg-emerald-950/40"
              }`}
              onClick={() => setSide("BUY")}
            >
              <div className="flex items-center justify-center gap-1">
                <span className="text-sm">↗</span>
                <span>BUY</span>
              </div>
            </button>
            <button
              type="button"
              className={`py-2 px-3 rounded-lg font-semibold text-xs transition-all duration-200 ${
                side === "SELL"
                  ? "bg-red-600 text-white shadow-md shadow-red-600/25"
                  : "bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-950/40"
              }`}
              onClick={() => setSide("SELL")}
            >
              <div className="flex items-center justify-center gap-1">
                <span className="text-sm">↘</span>
                <span>SELL</span>
              </div>
            </button>
          </div>
        </div>


        {/* Price Input (for Limit orders) - Compact */}
        {orderType === "LIMIT" && (
          <div className="mb-4">
            <label className="block text-xs font-semibold text-foreground mb-1.5">Limit Price</label>
            <div className="relative">
              <input
                type="number"
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                className="w-full p-2.5 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm pr-14 font-medium focus:border-primary focus:ring-1 focus:ring-primary/20 transition-all duration-200"
                step="0.01"
                min="0"
                required
                placeholder="Enter price"
              />
              <div className="absolute right-1.5 top-1/2 transform -translate-y-1/2 flex border border-border/50 rounded-sm overflow-hidden">
                <button
                  type="button"
                  className="bg-muted/50 p-1 hover:bg-muted transition-colors"
                  onClick={() => setPrice((Number.parseFloat(price) + 0.01).toString())}
                >
                  <ChevronUp className="h-2.5 w-2.5" />
                </button>
                <button
                  type="button"
                  className="bg-muted/50 p-1 hover:bg-muted transition-colors border-l border-border/50"
                  onClick={() => setPrice(Math.max(0.01, Number.parseFloat(price) - 0.01).toString())}
                >
                  <ChevronDown className="h-2.5 w-2.5" />
                </button>
              </div>
            </div>
            <div className="mt-1.5 text-xs text-muted-foreground">
              Market: <span className="font-medium text-foreground">{lastPrice}</span>
            </div>
          </div>
        )}

        {/* Quantity Input - Compact */}
        <div className="mb-4">
          <label className="block text-xs font-semibold text-foreground mb-1.5">Quantity</label>
          <input
            type="number"
            value={quantity}
            onChange={(e) => setQuantity(e.target.value)}
            className="w-full p-2.5 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm font-medium focus:border-primary focus:ring-1 focus:ring-primary/20 transition-all duration-200"
            step="0.0001"
            min="0"
            required
            placeholder="Enter quantity"
          />
          <div className="flex gap-1.5 mt-2">
            {percentButtons.map((percent) => (
              <button
                key={percent}
                type="button"
                onClick={() => handlePercentClick(percent)}
                className="flex-1 text-xs bg-muted/50 border border-border/50 px-2 py-1.5 rounded-md hover:bg-muted hover:border-border font-medium transition-all duration-200"
              >
                {percent}%
              </button>
            ))}
          </div>
          <div className="mt-1.5 text-xs text-muted-foreground">
            Max: <span className="font-medium text-foreground">{calculateMaxQuantity()}</span>
          </div>
        </div>

        {/* Stop Loss and Take Profit - Compact */}
        <div className="mb-4">
          <div
            className="flex justify-between items-center p-2.5 border border-border/50 rounded-lg cursor-pointer mb-2 hover:border-border transition-all duration-200 bg-muted/20"
            onClick={() => setShowSlTp(!showSlTp)}
          >
            <div className="flex items-center">
              <Shield className="h-3.5 w-3.5 mr-2 text-primary" />
              <span className="text-xs font-semibold">SL & TP</span>
            </div>
            <ChevronDown className={`h-3.5 w-3.5 transition-transform duration-200 ${showSlTp ? "rotate-180" : ""}`} />
          </div>

          {showSlTp && (
            <div className="p-3 border border-border/30 rounded-lg bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm space-y-3">
              {/* Stop Loss */}
              <div>
                <div className="flex justify-between items-center mb-1.5">
                  <label className="text-xs font-semibold text-foreground">Stop Loss</label>
                  <span
                    className={`text-xs font-bold ${Number.parseFloat(potentialPnl.sl) >= 0 ? "text-emerald-500" : "text-red-500"}`}
                  >
                    {potentialPnl.sl} USDT
                  </span>
                </div>
                <input
                  type="number"
                  value={stopLoss}
                  onChange={(e) => setStopLoss(e.target.value)}
                  className="w-full p-2 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm font-medium focus:border-red-400 focus:ring-1 focus:ring-red-400/20 transition-all duration-200 mb-1.5"
                  step="0.01"
                  min="0"
                  placeholder={side === "BUY" ? "Lower than entry" : "Higher than entry"}
                />
                <div className="flex gap-1.5">
                  {[1, 2, 5, 10].map((percent) => (
                    <button
                      key={percent}
                      type="button"
                      className={`flex-1 text-xs px-1.5 py-1 rounded-md font-medium transition-all duration-200 ${
                        slPercent === percent
                          ? "bg-red-500 text-white shadow-sm"
                          : "bg-muted/50 border border-border/50 hover:bg-muted hover:border-border text-muted-foreground hover:text-foreground"
                      }`}
                      onClick={() => calculateSlTp(percent, "sl")}
                    >
                      -{percent}%
                    </button>
                  ))}
                </div>
              </div>

              {/* Take Profit */}
              <div>
                <div className="flex justify-between items-center mb-1.5">
                  <label className="text-xs font-semibold text-foreground">Take Profit</label>
                  <span
                    className={`text-xs font-bold ${Number.parseFloat(potentialPnl.tp) >= 0 ? "text-emerald-500" : "text-red-500"}`}
                  >
                    {potentialPnl.tp} USDT
                  </span>
                </div>
                <input
                  type="number"
                  value={takeProfit}
                  onChange={(e) => setTakeProfit(e.target.value)}
                  className="w-full p-2 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm font-medium focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400/20 transition-all duration-200 mb-1.5"
                  step="0.01"
                  min="0"
                  placeholder={side === "BUY" ? "Higher than entry" : "Lower than entry"}
                />
                <div className="flex gap-1.5">
                  {[1, 2, 5, 10].map((percent) => (
                    <button
                      key={percent}
                      type="button"
                      className={`flex-1 text-xs px-1.5 py-1 rounded-md font-medium transition-all duration-200 ${
                        tpPercent === percent
                          ? "bg-emerald-500 text-white shadow-sm"
                          : "bg-muted/50 border border-border/50 hover:bg-muted hover:border-border text-muted-foreground hover:text-foreground"
                      }`}
                      onClick={() => calculateSlTp(percent, "tp")}
                    >
                      +{percent}%
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Order Summary - Compact */}
        <div className="bg-gradient-to-r from-muted/60 to-muted/40 p-3 rounded-lg border border-border/30 mb-4">
          <h3 className="text-xs font-bold text-foreground mb-2">Order Summary</h3>
          <div className="space-y-1.5">
            <div className="flex justify-between items-center">
              <span className="text-xs text-muted-foreground">Value:</span>
              <span className="font-semibold text-foreground text-sm">{orderValue.toFixed(2)} USDT</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs text-muted-foreground">Margin:</span>
              <span className={`font-semibold text-sm ${!hasSufficientBalance ? "text-red-500" : "text-foreground"}`}>
                {requiredMargin.toFixed(2)} USDT
              </span>
            </div>
            {!hasSufficientBalance && quantity && (
              <div className="mt-2 p-2 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-md">
                <div className="text-red-600 dark:text-red-400 text-xs font-semibold">
                  ⚠️ Insufficient Balance
                </div>
                <div className="text-red-500 dark:text-red-400 text-xs mt-0.5">
                  Need {(requiredMargin - availableBalance).toFixed(2)} USDT more
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isLoading || !hasSufficientBalance}
          className={`w-full py-4 rounded-xl font-bold text-base mt-auto transition-all duration-300 transform ${
            side === "BUY"
              ? "bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 shadow-lg shadow-emerald-600/25"
              : "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 shadow-lg shadow-red-600/25"
          } text-white ${
            (isLoading || !hasSufficientBalance)
              ? "opacity-70 cursor-not-allowed"
              : "hover:scale-105 hover:shadow-xl active:scale-95"
          }`}
        >
          {isLoading ? (
            <span className="flex items-center justify-center">
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <span className="text-lg">Processing Order...</span>
            </span>
          ) : (
            <div className="flex items-center justify-center">
              <span className="text-xl mr-2">{side === "BUY" ? "📈" : "📉"}</span>
              <span className="text-lg font-bold">
                {`${side === "BUY" ? "BUY / LONG" : "SELL / SHORT"} ${symbol.replace("USDT", "")}`}
              </span>
            </div>
          )}
        </button>
      </form>
    </div>
  )
}

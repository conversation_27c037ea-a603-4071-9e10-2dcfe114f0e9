"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\pages\\_document.js":
/*!************************************************************************************!*\
  !*** C:\Users\<USER>\AppData\Roaming\npm\node_modules\next\dist\pages\_document.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Head: function() {\n        return Head;\n    },\n    NextScript: function() {\n        return NextScript;\n    },\n    Html: function() {\n        return Html;\n    },\n    Main: function() {\n        return Main;\n    },\n    /**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */ default: function() {\n        return Document;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! react */ \"react?1189\"));\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nconst _getpagefiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nconst _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nconst _iserror = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! ../lib/is-error */ \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\next\\\\dist\\\\lib\\\\is-error.js\"));\nconst _htmlcontext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n/** Set of pages that have triggered a large data warning on production mode. */ const largePageDataWarnings = new Set();\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getpagefiles.getPageFiles)(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getpagefiles.getPageFiles)(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix, buildManifest, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${assetQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var _el_props, _el_props_dangerouslySetInnerHTML;\n            return el == null ? void 0 : (_el_props = el.props) == null ? void 0 : (_el_props_dangerouslySetInnerHTML = _el_props.dangerouslySetInnerHTML) == null ? void 0 : _el_props_dangerouslySetInnerHTML.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports, assetPrefix, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var _buildManifest_lowPriorityFiles;\n    const { assetPrefix, buildManifest, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (_buildManifest_lowPriorityFiles = buildManifest.lowPriorityFiles) == null ? void 0 : _buildManifest_lowPriorityFiles.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix, scriptLoader, crossOrigin, nextScriptWorkers } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var _child_props, _child_props_dangerouslySetInnerHTML;\n            return hasComponentProps(child) && (child == null ? void 0 : (_child_props = child.props) == null ? void 0 : (_child_props_dangerouslySetInnerHTML = _child_props.dangerouslySetInnerHTML) == null ? void 0 : _child_props_dangerouslySetInnerHTML.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy, src, children: scriptChildren, dangerouslySetInnerHTML, ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                };\n            } else {\n                throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                ...srcProps,\n                ...scriptProps,\n                type: \"text/partytown\",\n                key: src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            });\n        }));\n    } catch (err) {\n        if ((0, _iserror.default)(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader, disableOptimizedLoading, crossOrigin } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy, ...scriptProps } = file;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            ...scriptProps,\n            key: scriptProps.src || index,\n            defer: scriptProps.defer ?? !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin, nonce, ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nfunction getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix = \"\") {\n    if (!nextFontManifest) {\n        return {\n            preconnect: null,\n            preload: null\n        };\n    }\n    const appFontsEntry = nextFontManifest.pages[\"/_app\"];\n    const pageFontsEntry = nextFontManifest.pages[dangerousAsPath];\n    const preloadedFontFiles = [\n        ...appFontsEntry ?? [],\n        ...pageFontsEntry ?? []\n    ];\n    // If no font files should preload but there's an entry for the path, add a preconnect tag.\n    const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n    return {\n        preconnect: preconnectToSelf ? /*#__PURE__*/ _react.default.createElement(\"link\", {\n            \"data-next-font\": nextFontManifest.pagesUsingSizeAdjust ? \"size-adjust\" : \"\",\n            rel: \"preconnect\",\n            href: \"/\",\n            crossOrigin: \"anonymous\"\n        }) : null,\n        preload: preloadedFontFiles ? preloadedFontFiles.map((fontFile)=>{\n            const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: fontFile,\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${encodeURI(fontFile)}`,\n                as: \"font\",\n                type: `font/${ext}`,\n                crossOrigin: \"anonymous\",\n                \"data-next-font\": fontFile.includes(\"-s\") ? \"size-adjust\" : \"\"\n            });\n        }) : null\n    };\n}\nclass Head extends _react.default.Component {\n    static #_ = (()=>{\n        this.contextType = _htmlcontext.HtmlContext;\n    })();\n    getCssLinks(files) {\n        const { assetPrefix, assetQueryString, dynamicImports, crossOrigin, optimizeCss, optimizeFonts } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports, assetPrefix, assetQueryString, crossOrigin } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix, assetQueryString, scriptLoader, crossOrigin } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }))\n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader } = this.context;\n        const { nonce, crossOrigin } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy, children, dangerouslySetInnerHTML, src, ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                ...scriptProps,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            });\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var _c_props, _c_props1;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (_c_props = c.props) == null ? void 0 : _c_props.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url })=>{\n                var _c_props, _c_props_href;\n                return c == null ? void 0 : (_c_props = c.props) == null ? void 0 : (_c_props_href = _c_props.href) == null ? void 0 : _c_props_href.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (_c_props1 = c.props) == null ? void 0 : _c_props1.children) {\n                const newProps = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            }\n            return c;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles, ampPath, inAmpMode, hybridAmp, canonicalBase, __NEXT_DATA__, dangerousAsPath, headTags, unstable_runtimeJS, unstable_JsPreload, disableOptimizedLoading, optimizeCss, optimizeFonts, assetPrefix, nextFontManifest } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                let metaTag;\n                if (this.context.strictNextHead) {\n                    metaTag = /*#__PURE__*/ _react.default.createElement(\"meta\", {\n                        name: \"next-head\",\n                        content: \"1\"\n                    });\n                }\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    metaTag && cssPreloads.push(metaTag);\n                    cssPreloads.push(c);\n                } else {\n                    if (c) {\n                        if (metaTag && (c.type !== \"meta\" || !c.props[\"charSet\"])) {\n                            otherHeadElements.push(metaTag);\n                        }\n                        otherHeadElements.push(c);\n                    }\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var _child_props;\n                const isReactHelmet = child == null ? void 0 : (_child_props = child.props) == null ? void 0 : _child_props[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var _child_props1;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (_child_props1 = child.props) == null ? void 0 : _child_props1.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type, props } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        const nextFontLinkTags = getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix);\n        return /*#__PURE__*/ _react.default.createElement(\"head\", getHeadHTMLProps(this.props), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, this.context.strictNextHead ? null : /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }), nextFontLinkTags.preconnect, nextFontLinkTags.preload,  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var _children_find, _children_find_props, _children_find1, _children_find_props1;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (_children_find = children.find((child)=>child.type === Head)) == null ? void 0 : (_children_find_props = _children_find.props) == null ? void 0 : _children_find_props.children;\n    const bodyChildren = (_children_find1 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (_children_find_props1 = _children_find1.props) == null ? void 0 : _children_find_props1.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ]\n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var _child_type;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((_child_type = child.type) == null ? void 0 : _child_type.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }\n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static #_ = (()=>{\n        this.contextType = _htmlcontext.HtmlContext;\n    })();\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__, largePageDataBytes } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n                return (0, _htmlescape.htmlEscapeJsonString)(data);\n            }\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\next\\\\dist\\\\lib\\\\pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                if (false) {}\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape.htmlEscapeJsonString)(data);\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix, inAmpMode, buildManifest, unstable_runtimeJS, docComponentsRendered, assetQueryString, disableOptimizedLoading, crossOrigin } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles\n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${assetQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nfunction Html(props) {\n    const { inAmpMode, docComponentsRendered, locale, scriptLoader, __NEXT_DATA__ } = (0, _htmlcontext.useHtmlContext)();\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement(\"html\", {\n        ...props,\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    });\n}\nfunction Main() {\n    const { docComponentsRendered } = (0, _htmlcontext.useHtmlContext)();\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\pages\\_document.js\n");

/***/ }),

/***/ "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\lib\\is-error.js":
/*!*********************************************************************************!*\
  !*** C:\Users\<USER>\AppData\Roaming\npm\node_modules\next\dist\lib\is-error.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return isError;\n    },\n    getProperError: function() {\n        return getProperError;\n    }\n});\nconst _isplainobject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isplainobject.isPlainObject)(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\lib\\is-error.js\n");

/***/ }),

/***/ "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\lib\\pretty-bytes.js":
/*!*************************************************************************************!*\
  !*** C:\Users\<USER>\AppData\Roaming\npm\node_modules\next\dist\lib\pretty-bytes.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return prettyBytes;\n    }\n}));\nconst UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\lib\\pretty-bytes.js\n");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react?1189":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\pages\\_document.js"));
module.exports = __webpack_exports__;

})();